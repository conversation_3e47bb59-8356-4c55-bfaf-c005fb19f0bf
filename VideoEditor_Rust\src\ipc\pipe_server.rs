// ******************************************************
// 文件名: pipe_server.rs
// 功能描述: 简化的命名管道服务器
// ******************************************************

use std::io::{Write, BufRead, BufReader};
use interprocess::local_socket::{LocalSocketStream, LocalSocketListener};
use log::{info, warn, error};
use tokio::sync::mpsc::{self, Receiver, Sender};

use super::{Message, UniversalMessage, Command};

/// 默认管道名称
const DEFAULT_PIPE_NAME: &str = "ecut_pro_pipe";

/// 命令管道名称后缀
const COMMAND_PIPE_SUFFIX: &str = "_cmd";

/// 命名管道服务器
///
/// 负责与前端C#应用程序进行双向通信：
/// - 发送进度、状态等消息到前端
/// - 接收前端发送的处理命令
pub struct PipeServer {
    /// 消息发送通道
    sender: Sender<Message>,
    /// 管道名称
    pipe_name: String,
}

impl PipeServer {
    /// 创建新的命名管道服务器
    ///
    /// # 参数
    /// * `pipe_name` - 可选的管道名称，默认为"ecut_pro_pipe"
    /// * `disconnect_handler` - 断连处理函数，连接失败时会被调用
    ///
    /// # 返回
    /// 新创建的管道服务器实例
    pub fn new<F>(pipe_name: Option<&str>, disconnect_handler: F) -> Self
    where
        F: Fn() + Send + 'static
    {
        // 创建消息通道
        let (sender, receiver) = mpsc::channel::<Message>(100);
        let pipe_name = pipe_name.unwrap_or(DEFAULT_PIPE_NAME).to_string();

        // 启动消息处理任务
        let pipe_name_clone = pipe_name.clone();
        tokio::spawn(async move {
            Self::handle_messages(receiver, pipe_name_clone, disconnect_handler).await;
        });

        Self {
            sender,
            pipe_name,
        }
    }
    
    /// 发送视频处理消息（进度、状态等）
    pub async fn send_video_processing(&self, message: UniversalMessage) {
        let _ = self.sender.send(Message::VideoProcessing(message)).await;
    }

    /// 发送预览结果消息
    pub async fn send_preview(&self, message: UniversalMessage) {
        let _ = self.sender.send(Message::Preview(message)).await;
    }

    /// 发送GPU检查结果消息
    pub async fn send_gpu_check(&self, message: UniversalMessage) {
        let _ = self.sender.send(Message::GpuCheck(message)).await;
    }

    /// 发送视频分析结果消息
    pub async fn send_video_analysis(&self, message: UniversalMessage) {
        let _ = self.sender.send(Message::VideoAnalysis(message)).await;
    }
    
    /// 稳定的消息处理循环 - 扁平化风格，保持连接
    ///
    /// 特点：
    /// 1. 扁平化早返回模式 - 减少嵌套
    /// 2. 保持长连接，避免频繁重连
    /// 3. 3次连续失败后触发优雅退出
    /// 4. 简洁的错误处理
    async fn handle_messages<F>(
        mut receiver: Receiver<Message>,
        pipe_name: String,
        disconnect_handler: F
    )
    where
        F: Fn() + Send + 'static
    {
        let mut connection: Option<LocalSocketStream> = None;
        let mut consecutive_failures = 0;
        const MAX_FAILURES: u32 = 3;

        while let Some(message) = receiver.recv().await {
            // 处理单条消息 - 扁平化风格
            let should_exit = Self::handle_single_message(
                &mut connection,
                &mut consecutive_failures,
                &pipe_name,
                &message,
            ).await;

            // 早返回 - 检查是否需要退出
            if should_exit || consecutive_failures >= MAX_FAILURES {
                error!("管道连接持续失败，前端可能已断开，触发优雅停止");
                disconnect_handler();
                break;
            }
        }

        info!("消息处理器已停止");
    }

    /// 处理单条消息 - 扁平化风格
    async fn handle_single_message(
        connection: &mut Option<LocalSocketStream>,
        consecutive_failures: &mut u32,
        pipe_name: &str,
        message: &Message,
    ) -> bool {
        // 确保连接存在
        if connection.is_none() {
            *connection = Self::try_connect(pipe_name, consecutive_failures).await;

            // 早返回 - 连接失败
            if connection.is_none() {
                return true; // 需要退出
            }
        }

        // 发送消息
        let conn = connection.as_mut().unwrap(); // 此时连接必定存在
        match Self::send_message_safe(conn, message).await {
            Ok(_) => {
                *consecutive_failures = 0; // 重置失败计数
                false // 继续处理
            }
            Err(_) => {
                *consecutive_failures += 1;
                *connection = None; // 标记连接失效

                warn!("发送失败 ({}/3), 将在下次消息时重连", consecutive_failures);
                false // 让上层检查是否达到最大失败次数
            }
        }
    }

    /// 尝试连接 - 扁平化风格
    async fn try_connect(
        pipe_name: &str,
        consecutive_failures: &mut u32,
    ) -> Option<LocalSocketStream> {
        match LocalSocketStream::connect(pipe_name) {
            Ok(conn) => {
                info!("管道连接成功: {}", pipe_name);
                Some(conn)
            }
            Err(e) => {
                *consecutive_failures += 1;
                warn!("连接失败 ({}/3): {}", consecutive_failures, e);

                // 短暂等待
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                None
            }
        }
    }

    /// 安全发送消息 - 统一错误处理
    async fn send_message_safe(
        conn: &mut LocalSocketStream,
        message: &Message,
    ) -> Result<(), String> {
        let json = serde_json::to_string(message)
            .map_err(|e| format!("序列化失败: {}", e))?;

        writeln!(conn, "{}", json)
            .map_err(|e| format!("发送失败: {}", e))?;

        Ok(())
    }

    /// 启动异步命令监听器 - 符合tokio架构
    ///
    /// 在异步任务中监听来自前端的命令，并调用提供的处理函数
    /// 使用tokio::spawn而不是std::thread，更好地集成到异步运行时
    ///
    /// # 参数
    /// * `command_handler` - 命令处理函数
    pub fn start_command_listener<F>(&self, command_handler: F)
    where
        F: Fn(Command) + Send + 'static + Clone
    {
        let pipe_name = format!("{}{}", self.pipe_name, COMMAND_PIPE_SUFFIX);

        // 使用tokio::spawn启动异步监听器
        tokio::spawn(async move {
            Self::run_command_listener(pipe_name, command_handler).await;
        });
    }

    /// 真正的扁平化命令监听器
    async fn run_command_listener<F>(pipe_name: String, command_handler: F)
    where
        F: Fn(Command) + Send + 'static + Clone
    {
        // 启动监听器
        let listener = match Self::create_listener(&pipe_name) {
            Ok(listener) => listener,
            Err(_) => return, // 早返回
        };

        info!("命令监听器已启动: {}", pipe_name);

        // 在阻塞任务中运行监听循环
        let _ = tokio::task::spawn_blocking(move || {
            Self::listen_loop(listener, command_handler)
        }).await;

        info!("命令监听器已停止");
    }

    /// 创建监听器 - 扁平化错误处理
    fn create_listener(pipe_name: &str) -> Result<LocalSocketListener, std::io::Error> {
        match LocalSocketListener::bind(pipe_name) {
            Ok(listener) => Ok(listener),
            Err(e) => {
                error!("命令监听器启动失败: {}", e);
                Err(e)
            }
        }
    }

    /// 监听循环 - 扁平化风格
    fn listen_loop<F>(listener: LocalSocketListener, command_handler: F)
    where
        F: Fn(Command) + Send + 'static + Clone
    {
        for stream in listener.incoming() {
            // 早返回 - 连接失败直接跳过
            let stream = match stream {
                Ok(s) => s,
                Err(_) => continue,
            };

            // 早返回 - 命令解析失败直接跳过
            let cmd = match Self::read_command(stream) {
                Ok(c) => c,
                Err(_) => continue,
            };

            // 处理命令
            command_handler(cmd);
        }
    }

    /// 从连接中读取并解析命令
    ///
    /// # 参数
    /// * `stream` - 命名管道连接
    ///
    /// # 返回
    /// * `Result<Command, String>` - 成功时返回解析的命令，失败时返回错误信息
    fn read_command(stream: LocalSocketStream) -> Result<Command, String> {
        let mut reader = BufReader::new(stream);
        let mut buffer = String::new();

        // 读取一行数据
        reader.read_line(&mut buffer)
            .map_err(|e| format!("读取命令失败: {}", e))?;

        // 解析JSON命令
        serde_json::from_str::<Command>(&buffer)
            .map_err(|e| format!("解析命令失败: {}", e))
    }
}

// 删除Default实现，强制用户提供正确的断连处理逻辑
// PipeServer必须通过new()方法创建，并提供合适的断连处理回调