﻿// ******************************************************
// 文件名: MainWindowViewModel.cs
// 功能描述: 主窗口视图模型
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 管理主界面的数据绑定和命令
//   2. 处理功能卡片的初始化和管理
//   3. 协调视频处理流程和状态更新
// ******************************************************

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using Avalonia.Controls;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ECutPro.Models;
using ECutPro.Services;
using ECutPro.VideoEffects;
using ECutPro.Converters;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace ECutPro.ViewModels;

/// <summary>
/// 主窗口视图模型 - 扁平化架构，学习Rust简洁风格
/// </summary>
/// <remarks>
/// 设计理念：
/// - 扁平化结构：删除过度的#region分组
/// - 统一错误处理：使用ExecuteWithLoading模式
/// - 事件驱动：直接订阅PipeVideoService事件
/// - 职责分离：UI逻辑与业务逻辑分离
/// </remarks>
public partial class MainWindowViewModel : ObservableObject
{
    #region 属性定义

    /// <summary>
    /// 视频效果列表
    /// </summary>
    [ObservableProperty] private ObservableCollection<VideoEffect> _videoEffects = new();

    /// <summary>
    /// 应用程序标题
    /// </summary>
    [ObservableProperty] private string _title = "E剪 Pro";

    /// <summary>
    /// 是否正在加载中
    /// </summary>
    [ObservableProperty] private bool _isLoading = false;

    /// <summary>
    /// 加载提示消息
    /// </summary>
    [ObservableProperty] private string _loadingMessage = string.Empty;

    /// <summary>
    /// 视频文件集合 - 直接引用FileManager的文件列表
    /// </summary>
    public ObservableCollection<VideoFileInfo> VideoFiles => App.FileManager.Files;

    /// <summary>
    /// 当前选中的视频文件索引
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(HasSelectedVideo))]
    private int _selectedIndex = -1;

    /// <summary>
    /// 当前选中视频的元数据信息
    /// </summary>
    [ObservableProperty] private VideoMetadata _videoMetadata = new VideoMetadata();

    /// <summary>
    /// 是否有选中的视频文件
    /// </summary>
    public bool HasSelectedVideo => SelectedIndex >= 0;

    /// <summary>
    /// 选中索引变更时的处理逻辑
    /// </summary>
    partial void OnSelectedIndexChanged(int value)
    {
        if (value >= 0 && value < VideoFiles.Count)
            LoadVideoMetadataAsync(VideoFiles[value].FilePath);
        else
            VideoMetadata = new VideoMetadata();
    }

    /// <summary>
    /// 视频文件总数
    /// </summary>
    [ObservableProperty] private int _totalVideos = 0;

    /// <summary>
    /// 已处理的视频数量
    /// </summary>
    [ObservableProperty] private int _processedVideos = 0;

    /// <summary>
    /// 处理失败的视频数量
    /// </summary>
    [ObservableProperty] private int _failedVideos = 0;

    /// <summary>
    /// 当前进度值 (0-100)
    /// </summary>
    [ObservableProperty] private double _progressValue = 0;

    /// <summary>
    /// 是否正在处理视频
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(ProcessButtonText))]
    private bool _isProcessing = false;

    /// <summary>
    /// 是否已加载视频文件
    /// </summary>
    [ObservableProperty] private bool _videoFilesLoaded = false;

    /// <summary>
    /// 进度百分比文本显示
    /// </summary>
    public string ProgressPercent => $"{ProgressValue:0}%";

    /// <summary>
    /// 处理按钮显示文本
    /// </summary>
    public string ProcessButtonText => IsProcessing ? "取消处理" : "开始处理";

    /// <summary>
    /// 丝滑进度管理器
    /// </summary>
    public SmoothProgressManager ProgressManager { get; } = new();

    /// <summary>
    /// 视频处理选项配置
    /// </summary>
    public ProcessingOptions ProcessingOptions => App.UserPreferences.ProcessingOptions;
    
    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 初始化主窗口视图模型
    /// </summary>
    public MainWindowViewModel()
    {
        InitializeVideoEffects();
        InitializeTcpServiceEvents();
        InitializeProcessingOptionsEvents();
    }

    /// <summary>
    /// 初始化视频效果列表
    /// </summary>
    private void InitializeVideoEffects()
    {
        VideoEffects.Clear();
        var effects = new List<VideoEffect>
        {
            new SpeedEffect(),
            new ColorAdjustmentEffect(),
            new CropEffect(),
            new WatermarkEffect()
        };

        foreach (var effect in effects)
        {
            effect.LoadSettingsFromStorage();
            VideoEffects.Add(effect);
        }
    }

    /// <summary>
    /// 初始化TCP服务事件订阅
    /// </summary>
    private void InitializeTcpServiceEvents()
    {
        App.TcpService.AnalysisReceived += OnVideoAnalysisReceived;
        App.TcpService.GpuCheckReceived += OnGpuCheckReceived;
        App.TcpService.VideoProcessingReceived += OnVideoProcessingReceived;
    }

    /// <summary>
    /// 初始化处理选项事件订阅
    /// </summary>
    private void InitializeProcessingOptionsEvents()
    {
        // 订阅ProcessingOptions的属性变化事件
        App.UserPreferences.ProcessingOptions.PropertyChanged += OnProcessingOptionsPropertyChanged;
    }

    /// <summary>
    /// 处理ProcessingOptions属性变化
    /// </summary>
    private async void OnProcessingOptionsPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // 当GPU加速选项改变时，自动检测GPU
        if (e.PropertyName == nameof(ProcessingOptions.GpuAccelerationIndex))
        {
            await CheckGpuAccelerationCommand.ExecuteAsync(null);
        }
    }

    #endregion

    #region 事件处理方法

    /// <summary>
    /// 处理视频分析结果
    /// </summary>
    /// <param name="analysis">分析结果消息</param>
    private void OnVideoAnalysisReceived(UniversalMessage analysis)
    {
        if (analysis.Success)
        {
            try
            {
                var data = JsonSerializer.Deserialize<VideoAnalysisData>(analysis.Data.GetRawText());
                if (data?.Metadata != null)
                {
                    VideoMetadata = data.Metadata;
                    return;
                }
            }
            catch (JsonException) { }
        }

        VideoMetadata = new VideoMetadata();
        if (!string.IsNullOrEmpty(analysis.Message))
            App.ShowToast($"视频分析失败: {analysis.Message}");
    }

    /// <summary>
    /// 处理GPU检查结果
    /// </summary>
    /// <param name="gpuCheck">GPU检查结果</param>
    private void OnGpuCheckReceived(UniversalMessage gpuCheck)
    {
        if (!gpuCheck.Success)
        {
            ProcessingOptions.GpuAccelerationIndex = 0;
            App.ShowToast(gpuCheck.Message ?? "GPU加速不可用，已切换到CPU处理");
        }
    }

    /// <summary>
    /// 处理视频处理消息 - 统一处理进度和状态
    /// </summary>
    /// <param name="message">视频处理消息</param>
    private void OnVideoProcessingReceived(UniversalMessage message)
    {
        if (message.IsProgressMessage())
        {
            // 处理进度消息
            var taskId = message.GetTaskId();
            var progress = message.GetProgress();
            var globalProgress = message.GetGlobalProgress();

            // 更新全局进度条
            _ = ProgressManager.UpdateProgressSmoothly(globalProgress);

            // 更新视频列表中单个文件的状态显示
            if (!string.IsNullOrEmpty(progress))
            {
                App.FileManager.UpdateFileStatus(taskId, progress);
            }

            // 如果是完成状态，更新全局统计
            if (progress == "处理完成")
            {
                ProcessedVideos++;
            }
        }
        else if (message.IsStatusMessage())
        {
            // 处理状态消息
            var taskId = message.GetTaskId();

            // 处理全局取消消息
            if (taskId == "global")
            {
                App.FileManager.CancelAllPendingTasks();
                App.ShowToast(message.Message ?? "已取消所有任务");
                return;
            }

            // 显示状态消息
            if (!string.IsNullOrEmpty(message.Message))
            {
                App.ShowToast(message.Message);
            }

            // 更新文件状态和全局统计
            // 注意：由于我们删除了TaskStatus枚举，这里需要根据实际情况调整
            // 暂时使用消息内容来判断状态
            if (message.Success)
            {
                App.FileManager.UpdateFileStatus(taskId, "已完成");
                ProcessedVideos++;
            }
            else
            {
                App.FileManager.UpdateFileStatus(taskId, "处理失败");
                FailedVideos++;
            }
        }
    }





    #endregion

    #region 加载状态管理

    /// <summary>
    /// 加载状态变更事件 - 通知UI更新加载状态
    /// </summary>
    public event Action<bool, string>? LoadingStateChanged;

    /// <summary>
    /// 设置加载状态
    /// </summary>
    /// <param name="isLoading">是否正在加载</param>
    /// <param name="message">加载消息</param>
    private void SetLoading(bool isLoading, string message = "")
    {
        IsLoading = isLoading;
        LoadingMessage = message;
        LoadingStateChanged?.Invoke(isLoading, message);
    }

    /// <summary>
    /// 通用的带加载状态的异步操作执行器
    /// </summary>
    /// <param name="loadingMessage">加载提示消息</param>
    /// <param name="errorMessage">失败时显示的友好错误消息</param>
    /// <param name="action">要执行的异步操作</param>
    private async Task ExecuteWithLoading(string loadingMessage, string errorMessage, Func<Task> action)
    {
        try
        {
            SetLoading(true, loadingMessage);
            await action();
        }
        catch (Exception ex)
        {
            App.ShowToast(errorMessage);
            System.Diagnostics.Debug.WriteLine($"[{errorMessage}] 详细错误: {ex}");
        }
        finally
        {
            SetLoading(false);
        }
    }

    #endregion

    #region 命令方法

    /// <summary>
    /// 添加视频文件命令
    /// </summary>
    [RelayCommand]
    private async Task AddVideoFilesAsync() =>
        await ExecuteWithLoading("正在选择文件...", "添加视频文件失败，请重试", AddVideoFilesCore);

    /// <summary>
    /// 添加文件夹命令
    /// </summary>
    [RelayCommand]
    private async Task AddFolderAsync() =>
        await ExecuteWithLoading("正在选择文件夹...", "添加文件夹失败，请检查文件夹权限", AddFolderCore);

    /// <summary>
    /// 选择输出路径命令
    /// </summary>
    [RelayCommand]
    private async Task SelectOutputPathAsync() =>
        await ExecuteWithLoading("正在选择路径...", "选择输出路径失败，请重试", SelectOutputPathCore);

    /// <summary>
    /// 检测GPU加速命令
    /// </summary>
    [RelayCommand]
    private async Task CheckGpuAccelerationAsync() =>
        await ExecuteWithLoading("正在检测GPU...", "GPU检测失败，请重试", CheckGpuAccelerationCore);

    /// <summary>
    /// 移除选中的视频文件命令
    /// </summary>
    [RelayCommand]
    private void RemoveSelectedVideos(IList? selectedItems)
    {
        if (selectedItems == null || selectedItems.Count == 0) return;

        var itemsToRemove = selectedItems.Cast<VideoFileInfo>().ToList();
        foreach (var item in itemsToRemove)
        {
            App.FileManager.RemoveFile(item);
        }

        UpdateFileCount();
        App.ShowToast($"已移除 {itemsToRemove.Count} 个视频文件");
    }

    /// <summary>
    /// 清空所有视频文件命令
    /// </summary>
    [RelayCommand]
    private void ClearAllVideos()
    {
        var count = App.FileManager.FileCount;
        App.FileManager.Clear();

        ProcessedVideos = 0;
        FailedVideos = 0;
        SelectedIndex = -1;
        UpdateFileCount();

        App.ShowToast($"已清空 {count} 个视频文件");
    }

    /// <summary>
    /// 开始处理视频命令
    /// </summary>
    [RelayCommand]
    private async Task StartProcessing() =>
        await ExecuteWithLoading("正在开始处理...", "开始处理失败，请重试", StartProcessingCore);

    /// <summary>
    /// 取消处理视频命令
    /// </summary>
    [RelayCommand]
    private async Task CancelProcessing() =>
        await ExecuteWithLoading("正在取消...", "取消处理失败，请重试", CancelProcessingCore);

    #endregion

    #region 私有方法

    /// <summary>
    /// 添加视频文件的核心逻辑
    /// </summary>
    private async Task AddVideoFilesCore()
    {
        var files = await App.DialogService.SelectVideoFilesAsync();
        if (files.Length > 0)
        {
            SetLoading(true, "正在添加文件...");
            App.FileManager.AddFiles(files);
            UpdateFileCount();
            App.ShowToast($"成功添加 {files.Length} 个文件");
        }
    }

    /// <summary>
    /// 添加文件夹的核心逻辑
    /// </summary>
    private async Task AddFolderCore()
    {
        var folderPath = await App.DialogService.SelectOutputFolderAsync();
        if (!string.IsNullOrEmpty(folderPath))
        {
            SetLoading(true, "正在扫描文件夹...");
            var videoFiles = Directory.EnumerateFiles(folderPath, "*.*", SearchOption.TopDirectoryOnly)
                .Where(file => FileDialogService.IsValidVideoFile(file))
                .ToArray();

            if (videoFiles.Length == 0)
            {
                App.ShowToast("文件夹中未找到支持的视频文件");
                return;
            }

            SetLoading(true, "正在添加文件...");
            App.FileManager.AddFiles(videoFiles);
            UpdateFileCount();
            App.ShowToast($"成功添加 {videoFiles.Length} 个文件");
        }
    }

    /// <summary>
    /// 选择输出路径的核心逻辑
    /// </summary>
    private async Task SelectOutputPathCore()
    {
        var path = await App.DialogService.SelectOutputFolderAsync();
        if (!string.IsNullOrEmpty(path))
        {
            App.UserPreferences.ProcessingOptions.OutputPath = path;
            App.ShowToast("输出路径设置成功");
        }
    }

    /// <summary>
    /// GPU加速检测的核心逻辑
    /// </summary>
    private async Task CheckGpuAccelerationCore()
    {
        int accelerationIndex = ProcessingOptions.GpuAccelerationIndex;
        if (accelerationIndex == 0) return;

        string? gpuType = accelerationIndex switch
        {
            1 => "amd", 2 => "nvidia", 3 => "intel", _ => null
        };

        if (gpuType != null)
            await App.TcpService.CheckGpuAccelerationAsync(gpuType);
    }

    /// <summary>
    /// 异步加载视频元数据 - 使用ExecuteWithLoading模式
    /// </summary>
    /// <param name="filePath">视频文件路径</param>
    [RelayCommand]
    private async Task LoadVideoMetadataAsync(string filePath) =>
        await ExecuteWithLoading("正在分析视频...", "视频分析失败，请重试",
            () => LoadVideoMetadataCore(filePath));

    /// <summary>
    /// 加载视频元数据的核心逻辑
    /// </summary>
    private async Task LoadVideoMetadataCore(string filePath)
    {
        await App.TcpService.AnalyzeVideoAsync(filePath);
    }

    /// <summary>
    /// 更新文件计数状态
    /// </summary>
    private void UpdateFileCount()
    {
        TotalVideos = App.FileManager.FileCount;
        VideoFilesLoaded = TotalVideos > 0;
    }

    /// <summary>
    /// 开始处理视频的核心逻辑
    /// </summary>
    private async Task StartProcessingCore()
    {
        // 检查前置条件
        if (!VideoFilesLoaded || VideoFiles.Count == 0)
        {
            App.ShowToast("请先添加视频文件");
            return;
        }

        // 初始化处理状态
        IsProcessing = true;
        ProgressManager.Reset();
        ProcessedVideos = 0;
        FailedVideos = 0;

        // 准备处理参数
        var inputPaths = VideoFiles.Select(v => v.FilePath).ToList();
        var outputDir = string.IsNullOrEmpty(ProcessingOptions.OutputPath)
            ? Path.GetDirectoryName(inputPaths[0]) ?? Environment.CurrentDirectory
            : ProcessingOptions.OutputPath;
        var enabledEffects = VideoEffects.Where(e => e.IsEnabled).ToList();



        try
        {
            // 调用处理服务
            await App.TcpService.ProcessVideosAsync(inputPaths, outputDir, enabledEffects, ProcessingOptions);

            // 处理成功后的状态更新
            ProcessedVideos = VideoFiles.Count;
            ProgressValue = 100;
            App.ShowToast($"成功处理 {ProcessedVideos} 个视频文件");
        }
        catch
        {
            // 处理失败的状态更新
            FailedVideos = VideoFiles.Count;
            throw; // 重新抛出异常，让ExecuteWithLoading处理
        }
        finally
        {
            // 重置处理状态
            IsProcessing = false;
        }
    }

    /// <summary>
    /// 取消处理视频的核心逻辑 - 简洁纯净
    /// </summary>
    private async Task CancelProcessingCore()
    {
        await App.TcpService.CancelAllTasksAsync();
        App.ShowToast("已取消处理");
        IsProcessing = false;
    }

    #endregion

    #region 数据结构

    /// <summary>
    /// 视频分析数据结构 - 匹配后端返回格式
    /// </summary>
    private sealed class VideoAnalysisData
    {
        [JsonPropertyName("video_path")]
        public string VideoPath { get; set; } = string.Empty;

        [JsonPropertyName("metadata")]
        public VideoMetadata? Metadata { get; set; }

        [JsonPropertyName("analysis_time")]
        public string AnalysisTime { get; set; } = string.Empty;
    }

    #endregion
}

